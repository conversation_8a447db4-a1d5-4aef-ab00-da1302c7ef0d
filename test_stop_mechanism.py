#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
停止机制测试脚本
用于验证微信自动化程序的停止功能是否正常工作

测试内容：
1. 启动GUI程序
2. 模拟点击停止按钮
3. 验证所有线程和进程是否正确终止
4. 验证程序是否能够正常退出

版本：1.0.0
创建时间：2025-01-28
"""

import time
import threading
import psutil
import os
import sys
from pathlib import Path

def test_stop_mechanism():
    """测试停止机制"""
    print("🧪 开始测试停止机制...")
    
    # 记录测试开始时的进程状态
    initial_processes = get_python_processes()
    print(f"📊 测试开始时Python进程数量: {len(initial_processes)}")
    
    try:
        # 导入GUI模块
        from wechat_automation_gui import WeChatAutomationGUI
        
        print("✅ 成功导入GUI模块")
        
        # 创建GUI实例
        gui = WeChatAutomationGUI()
        print("✅ 成功创建GUI实例")
        
        # 模拟启动自动化（不实际运行）
        print("🔄 模拟启动自动化流程...")
        gui.is_running = True
        gui.start_button.config(state='disabled')
        gui.stop_button.config(state='normal')
        
        # 等待一段时间
        time.sleep(2)
        
        # 测试停止功能
        print("🛑 测试停止功能...")
        gui.stop_automation()
        
        # 等待停止完成
        time.sleep(3)
        
        # 验证停止状态
        if not gui.is_running and gui.stop_requested:
            print("✅ 停止状态验证成功")
        else:
            print("❌ 停止状态验证失败")
        
        # 测试强制退出
        print("🚪 测试强制退出功能...")
        gui._force_exit()
        
        print("✅ 停止机制测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return False
    
    # 检查进程清理情况
    time.sleep(2)
    final_processes = get_python_processes()
    print(f"📊 测试结束时Python进程数量: {len(final_processes)}")
    
    if len(final_processes) <= len(initial_processes):
        print("✅ 进程清理验证成功")
        return True
    else:
        print("⚠️ 可能存在未清理的进程")
        return False

def get_python_processes():
    """获取当前所有Python进程"""
    python_processes = []
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            if proc.info['name'] in ['python.exe', 'pythonw.exe']:
                python_processes.append(proc.info)
    except Exception as e:
        print(f"获取进程信息失败: {e}")
    return python_processes

def test_thread_termination():
    """测试线程终止功能"""
    print("\n🧪 开始测试线程终止功能...")
    
    def dummy_thread():
        """模拟长时间运行的线程"""
        try:
            while True:
                time.sleep(0.1)
        except SystemExit:
            print("✅ 线程收到SystemExit信号")
            return
    
    # 创建测试线程
    test_thread = threading.Thread(target=dummy_thread, daemon=True)
    test_thread.start()
    
    print(f"📊 测试线程已启动，ID: {test_thread.ident}")
    
    # 等待线程运行
    time.sleep(1)
    
    # 测试强制终止
    try:
        import ctypes
        thread_id = test_thread.ident
        if thread_id:
            ctypes.pythonapi.PyThreadState_SetAsyncExc(
                ctypes.c_long(thread_id),
                ctypes.py_object(SystemExit)
            )
            print("🛑 已发送线程终止信号")
    except Exception as e:
        print(f"❌ 线程终止失败: {e}")
        return False
    
    # 等待线程结束
    test_thread.join(timeout=2)
    
    if not test_thread.is_alive():
        print("✅ 线程终止验证成功")
        return True
    else:
        print("❌ 线程终止验证失败")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 微信自动化停止机制测试")
    print("=" * 60)
    
    # 检查依赖
    try:
        import tkinter
        import psutil
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 依赖检查失败: {e}")
        return
    
    # 测试1: 线程终止功能
    thread_test_result = test_thread_termination()
    
    # 测试2: 停止机制
    stop_test_result = test_stop_mechanism()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"   线程终止测试: {'✅ 通过' if thread_test_result else '❌ 失败'}")
    print(f"   停止机制测试: {'✅ 通过' if stop_test_result else '❌ 失败'}")
    
    if thread_test_result and stop_test_result:
        print("🎉 所有测试通过！停止机制工作正常")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
